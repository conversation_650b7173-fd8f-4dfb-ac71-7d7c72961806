/* VijinoWorld Theme - Preloader Styles */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff8e1;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
}

.preloader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loader {
    position: relative;
    width: 120px;
    height: 120px;
}

.loader-circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top-color: #af492b;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
}

.loader-circle:nth-child(2) {
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
    border-top-color: #b6902f;
    animation-duration: 1.6s;
    animation-direction: reverse;
}

.loader-circle:nth-child(3) {
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
    border-top-color: #e6d672;
    animation-duration: 0.8s;
}

.loader-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    opacity: 0;
    animation: pulse 1.5s ease-in-out infinite alternate, fade-in 0.5s ease-in-out forwards;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

/* VijinoWorld animation elements */
.plate-loader {
    position: absolute;
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    border: 1px solid rgba(175, 73, 43, 0.2);
}

/* Steam animation for preloader */
.steam {
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    opacity: 0;
    animation: steamRise 2s ease-out infinite;
}

@keyframes steamRise {
    0% {
        opacity: 0.3;
        transform: translateX(-50%) translateY(0) scale(0.5);
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px) scale(1.2);
    }
}