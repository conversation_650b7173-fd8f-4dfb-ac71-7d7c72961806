/* VijinoWorld Theme - Main Stylesheet */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #fff8e1, #fffde7, #fff8e1);
    color: #2c3522;
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

section {
    padding: 100px 0;
}

a {
    text-decoration: none;
    color: #af492b;
    transition: all 0.3s ease;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    opacity: 0.7;
}

/* Header */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: 20px 0;
    background: rgba(255, 248, 225, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(175, 73, 43, 0.1);
    min-height: 140px;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
}

header .logo img,
.logo img,
img[alt="VijinoWorld Logo"] {
    height: 120px !important;
    width: auto !important;
    margin-right: 0;
    transition: transform 0.3s ease;
    max-height: none !important;
    min-height: 120px !important;
    display: block !important;
    object-fit: contain !important;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Logo text styles removed - using image only */

nav ul.desktop-nav {
    display: flex;
}

nav ul.desktop-nav li {
    margin-left: 30px;
}

nav ul.desktop-nav li a {
    font-weight: 500;
    position: relative;
    padding-bottom: 5px;
    color: #5d4037;
    font-size: 1.1rem;
}

nav ul.desktop-nav li a:hover,
nav ul.desktop-nav li a.active {
    color: #af492b;
}

nav ul.desktop-nav li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #af492b;
    transition: width 0.3s ease;
}

nav ul.desktop-nav li a:hover::after,
nav ul.desktop-nav li a.active::after {
    width: 100%;
}

.mobile-nav-toggle,
.mobile-nav,
.close-nav {
    display: none;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 160px 0 100px 0;
    overflow: hidden;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(175, 73, 43, 0.1) 0%, transparent 70%);
    z-index: -1;
    opacity: 0.7;
    animation: pulse 8s infinite alternate;
}

@keyframes pulse {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-badge {
    display: inline-block;
    background: linear-gradient(135deg, #2c3522, #af492b);
    color: #fff;
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(175, 73, 43, 0.3);
    animation: float 3s ease-in-out infinite;
}

/* Main Title Effect */
.main-title {
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
    font-size: 4.5rem;
    font-weight: 700;
    text-transform: uppercase;
    position: relative;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #af492b, #b6902f);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.hero h2 {
    font-size: 2rem;
    margin: 0 0 20px;
    color: #5d4037;
    font-weight: 500;
}

.typed-text {
    color: #af492b;
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
    position: relative;
}

.typed-text::after {
    content: '|';
    position: absolute;
    right: -8px;
    animation: blink 0.8s infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

.hero-description {
    max-width: 600px;
    margin: 0 auto 40px;
    color: #2c3522;
    font-size: 1.1rem;
    line-height: 1.7;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

/* Countdown */
.countdown-container {
    margin-bottom: 50px;
}

.countdown-header {
    text-align: center;
    margin-bottom: 20px;
}

.launch-date {
    display: inline-block;
    padding: 8px 16px;
    background: rgba(175, 73, 43, 0.1);
    border-radius: 30px;
    font-size: 0.9rem;
    color: #2c3522;
    letter-spacing: 1px;
    border: 1px solid rgba(175, 73, 43, 0.2);
}

.countdown {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.countdown-item {
    margin: 0 15px;
    text-align: center;
    min-width: 80px;
    position: relative;
}

.countdown-digit-container {
    background: #fff;
    border-radius: 10px;
    padding: 15px 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(175, 73, 43, 0.2);
    margin-bottom: 10px;
}

.countdown-digit-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(175, 73, 43, 0.3), transparent);
}

.countdown-item span:first-child {
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #af492b, #b6902f);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: block;
    position: relative;
    z-index: 1;
}

/* Flip animation for countdown digits */
.flip {
    animation: flipAnimation 0.6s ease-in-out;
}

@keyframes flipAnimation {
    0% {
        transform: rotateX(0deg);
        opacity: 1;
    }
    50% {
        transform: rotateX(90deg);
        opacity: 0.5;
    }
    100% {
        transform: rotateX(0deg);
        opacity: 1;
    }
}

.countdown-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #2c3522;
    display: block;
    margin-top: 5px;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

/* Progress bar */
.countdown-progress {
    max-width: 500px;
    margin: 0 auto;
    padding: 0 20px;
}

.countdown-progress-bar {
    height: 6px;
    background: linear-gradient(90deg, #af492b, #b6902f);
    border-radius: 3px;
    width: 0%;
    transition: width 1s ease;
    box-shadow: 0 0 10px rgba(175, 73, 43, 0.3);
    position: relative;
}

.countdown-progress-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 0.8rem;
    color: #2c3522;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

/* Launch message that replaces countdown when launched */
.launch-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin-bottom: 50px;
}

.launch-message span {
    font-size: 1.5rem;
    font-weight: 600;
    color: #af492b;
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
}

.launch-btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(45deg, #af492b, #b6902f);
    border-radius: 30px;
    color: #fff;
    font-weight: 600;
    letter-spacing: 1px;
    box-shadow: 0 10px 20px rgba(175, 73, 43, 0.2);
    transition: all 0.3s ease;
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
}

.launch-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 25px rgba(175, 73, 43, 0.3);
}

/* About Section */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #af492b, #b6902f);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
}

.section-header p {
    color: #2c3522;
    font-size: 1.1rem;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature {
    background: #fff;
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.4s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(175, 73, 43, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #af492b, #b6902f);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(175, 73, 43, 0.2);
}

.feature:hover::before {
    opacity: 1;
}

.feature-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 4px 10px;
    background: rgba(175, 73, 43, 0.1);
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    color: #af492b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #af492b, #b6902f);
    border-radius: 50%;
    font-size: 1.8rem;
    color: #fff;
    box-shadow: 0 10px 20px rgba(175, 73, 43, 0.2);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.feature:hover .feature-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 25px rgba(175, 73, 43, 0.3);
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(175, 73, 43, 0.3), rgba(182, 144, 47, 0.3));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature:hover .feature-icon::after {
    opacity: 1;
    animation: pulse-ring 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

.feature h3 {
    margin-bottom: 15px;
    font-size: 1.4rem;
    color: #2c3522;
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
}

.feature p {
    color: #2c3522;
    font-size: 0.95rem;
    line-height: 1.7;
    margin-bottom: 25px;
    flex-grow: 1;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.feature-footer {
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #af492b;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.feature-link i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.feature-link:hover {
    color: #b6902f;
}

.feature-link:hover i {
    transform: translateX(4px);
}

/* Contact Section */
.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
}

.social-link {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    color: #af492b;
}

.social-link:hover {
    background: linear-gradient(45deg, #af492b, #b6902f);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(175, 73, 43, 0.2);
    color: #fff;
}

.contact-info {
    text-align: center;
    font-size: 1.1rem;
    color: #2c3522;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.contact-info a {
    color: #af492b;
    font-weight: 500;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* Footer */
footer {
    background: rgba(255, 248, 225, 0.9);
    backdrop-filter: blur(10px);
    padding: 30px 0;
    margin-top: 50px;
    border-top: 1px solid rgba(175, 73, 43, 0.1);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright {
    color: #2c3522;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.footer-links a {
    margin-left: 20px;
    color: #2c3522;
    transition: all 0.3s ease;
    font-family: 'Gilbeth', 'Poppins', sans-serif;
}

.footer-links a:hover {
    color: #af492b;
}

/* Reveal Animation */
.reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.reveal.active {
    opacity: 1;
    transform: translateY(0);
}

/* E-commerce specific styles */
.product-preview {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 40px;
}

.product-card {
    width: 220px;
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(175, 73, 43, 0.1);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(175, 73, 43, 0.2);
}

.product-image {
    height: 160px;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.product-image img {
    max-width: 80%;
    max-height: 80%;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 10px;
    background: rgba(175, 73, 43, 0.9);
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-info {
    padding: 20px;
    text-align: center;
}

.product-name {
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: #2c3522;
    margin-bottom: 8px;
}

.product-price {
    font-family: 'Gilbeth', 'Poppins', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #af492b;
    margin-bottom: 15px;
}

.product-btn {
    display: inline-block;
    padding: 8px 20px;
    background: linear-gradient(45deg, #af492b, #b6902f);
    border-radius: 30px;
    color: #fff;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.product-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(175, 73, 43, 0.2);
}

/* Newsletter Section */
.newsletter {
    background: rgba(175, 73, 43, 0.05);
    padding: 60px 0;
    margin-top: 80px;
    border-top: 1px solid rgba(175, 73, 43, 0.1);
    border-bottom: 1px solid rgba(175, 73, 43, 0.1);
}

.newsletter-form {
    max-width: 500px;
    margin: 30px auto 0;
    display: flex;
    gap: 10px;
}

.newsletter-input {
    flex: 1;
    padding: 12px 20px;
    border-radius: 30px;
    border: 1px solid rgba(175, 73, 43, 0.2);
    font-family: 'Gilbeth', 'Poppins', sans-serif;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.newsletter-input:focus {
    border-color: #af492b;
    box-shadow: 0 0 0 3px rgba(175, 73, 43, 0.1);
}

.newsletter-btn {
    padding: 12px 25px;
    background: linear-gradient(45deg, #af492b, #b6902f);
    border: none;
    border-radius: 30px;
    color: #fff;
    font-family: 'Copperplate Gothic Bold', 'Poppins', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(175, 73, 43, 0.2);
}

.form-message {
    text-align: center;
    margin-top: 15px;
    min-height: 24px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .main-title {
        font-size: 3.8rem;
    }
    
    .hero h2 {
        font-size: 1.7rem;
    }
    
    .hero-description {
        font-size: 1rem;
        max-width: 90%;
    }
    
    .countdown-item {
        min-width: 70px;
    }
    
    .countdown-item span:first-child {
        font-size: 2.2rem;
    }
    
    .countdown-progress {
        max-width: 90%;
    }
    
    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.6rem;
    }
    
    .product-card {
        width: 200px;
    }
}

@media (max-width: 768px) {
    section {
        padding: 80px 0;
    }

    .hero {
        padding: 140px 0 80px 0;
    }
    
    .main-title {
        font-size: 3.2rem;
    }
    
    .hero h2 {
        font-size: 1.5rem;
    }
    
    .hero-description {
        font-size: 0.95rem;
        margin-bottom: 30px;
    }
    
    .hero-badge {
        font-size: 0.8rem;
        padding: 6px 14px;
    }
    
    .logo img {
        height: 90px !important;
        margin-right: 0;
        min-height: 90px !important;
    }

    header {
        padding: 15px 0;
        min-height: 120px;
    }

    /* Logo text styles removed - using image only */

    nav ul.desktop-nav {
        display: none;
    }
    
    .mobile-nav-toggle {
        display: block;
        font-size: 1.5rem;
        cursor: pointer;
        color: #af492b;
    }
    
    .mobile-nav {
        display: block;
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100vh;
        background: rgba(255, 248, 225, 0.95);
        backdrop-filter: blur(10px);
        z-index: 1001;
        padding: 80px 30px 30px;
        transition: right 0.3s ease;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.05);
    }
    
    .mobile-nav.active {
        right: 0;
    }
    
    .close-nav {
        display: block;
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 1.5rem;
        cursor: pointer;
        color: #af492b;
    }
    
    .mobile-nav ul li {
        margin-bottom: 20px;
    }
    
    .mobile-nav ul li a {
        font-size: 1.2rem;
        display: block;
        padding: 10px 0;
        color: #5d4037;
    }
    
    .countdown {
        flex-wrap: wrap;
    }
    
    .countdown-item {
        margin: 10px;
    }
    
    .countdown-digit-container {
        padding: 12px 8px;
    }
    
    .countdown-progress-labels {
        font-size: 0.7rem;
    }
    
    .section-header h2 {
        font-size: 2.2rem;
    }
    
    .features {
        gap: 20px;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-links a {
        margin: 0 10px;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .newsletter-btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .main-title {
        font-size: 2.6rem;
    }
    
    .hero h2 {
        font-size: 1.3rem;
    }
    
    .hero-description {
        font-size: 0.9rem;
        line-height: 1.6;
    }
    
    .countdown-header {
        margin-bottom: 15px;
    }
    
    .launch-date {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    
    .countdown-item span:first-child {
        font-size: 1.8rem;
    }
    
    .countdown-label {
        font-size: 0.8rem;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
    }
    
    .section-header p {
        font-size: 0.95rem;
    }
    
    .feature {
        padding: 25px 15px;
    }
    
    .feature-badge {
        top: 15px;
        right: 15px;
        font-size: 0.65rem;
        padding: 3px 8px;
    }
    
    .feature h3 {
        font-size: 1.2rem;
    }
    
    .feature p {
        font-size: 0.85rem;
    }
    
    .social-link {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
    
    .contact-info {
        font-size: 0.95rem;
    }
    
    .product-preview {
        gap: 15px;
    }
    
    .product-card {
        width: 100%;
        max-width: 250px;
    }
}