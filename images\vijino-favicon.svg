<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#af492b" />
      <stop offset="100%" stop-color="#b6902f" />
    </linearGradient>
    <linearGradient id="gradientLight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#b6902f" />
      <stop offset="100%" stop-color="#e6d672" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="16" cy="16" r="15" fill="#fff" stroke="url(#gradient)" stroke-width="1">
    <animate attributeName="stroke-width" values="1;1.5;1" dur="3s" repeatCount="indefinite" />
  </circle>
  
  <!-- G<PERSON> (Simplified) -->
  <g stroke="#e6d672" stroke-width="0.2" opacity="0.4">
    <path d="M5 10 L27 10" />
    <path d="M5 16 L27 16" />
    <path d="M5 22 L27 22" />
    <path d="M8 8 Q16 5 24 8" />
    <path d="M8 24 Q16 27 24 24" />
  </g>
  
  <!-- V Letter (Simplified) -->
  <path d="M11 10 L16 22 L21 10" fill="none" stroke="#2c3522" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" />
  
  <!-- W Letter (Simplified) -->
  <path d="M13 16 L15 21 L16 18 L17 21 L19 16" fill="none" stroke="#af492b" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
  
  <!-- Animated Elements -->
  <circle cx="11" cy="10" r="1" fill="#e6d672">
    <animate 
      attributeName="cy" 
      values="10;9.5;10" 
      dur="3s" 
      repeatCount="indefinite" />
  </circle>
  
  <circle cx="21" cy="10" r="1" fill="#e6d672">
    <animate 
      attributeName="cy" 
      values="10;9.5;10" 
      dur="3.5s" 
      repeatCount="indefinite" />
  </circle>
  
  <!-- Pulsing Effect -->
  <circle cx="16" cy="16" r="8" fill="none" stroke="url(#gradientLight)" stroke-width="0.5" opacity="0.3">
    <animate 
      attributeName="r" 
      values="8;9;8" 
      dur="3s" 
      repeatCount="indefinite" />
    <animate 
      attributeName="opacity" 
      values="0.3;0.1;0.3" 
      dur="3s" 
      repeatCount="indefinite" />
  </circle>
</svg>