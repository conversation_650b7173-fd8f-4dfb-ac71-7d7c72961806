<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#af492b" />
      <stop offset="100%" stop-color="#b6902f" />
    </linearGradient>
    <linearGradient id="gradientLight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#b6902f" />
      <stop offset="100%" stop-color="#e6d672" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle with Grid Pattern -->
  <circle cx="50" cy="50" r="45" fill="#fff" stroke="url(#gradient)" stroke-width="2" />
  
  <!-- Grid Pattern -->
  <g stroke="#e6d672" stroke-width="0.5" opacity="0.5">
    <!-- Horizontal Lines -->
    <path d="M10 30 L90 30" />
    <path d="M10 40 L90 40" />
    <path d="M10 50 L90 50" />
    <path d="M10 60 L90 60" />
    <path d="M10 70 L90 70" />
    
    <!-- Curved Lines -->
    <path d="M20 20 Q50 10 80 20" />
    <path d="M20 80 Q50 90 80 80" />
    <path d="M30 25 Q50 15 70 25" />
    <path d="M30 75 Q50 85 70 75" />
  </g>
  
  <!-- V Letter -->
  <path d="M30 30 L45 70 L60 30" fill="none" stroke="#2c3522" stroke-width="8" stroke-linecap="round" stroke-linejoin="round" />
  
  <!-- W Letter -->
  <path d="M40 50 L45 65 L50 55 L55 65 L60 50" fill="none" stroke="#af492b" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" />
  
  <!-- Animated Elements -->
  <circle cx="30" cy="30" r="3" fill="#e6d672">
    <animate 
      attributeName="cy" 
      values="30;28;30" 
      dur="3s" 
      repeatCount="indefinite" />
  </circle>
  
  <circle cx="60" cy="30" r="3" fill="#e6d672">
    <animate 
      attributeName="cy" 
      values="30;28;30" 
      dur="3.5s" 
      repeatCount="indefinite" />
  </circle>
  
  <!-- Pulsing Effect -->
  <circle cx="50" cy="50" r="25" fill="none" stroke="url(#gradientLight)" stroke-width="1" opacity="0.3">
    <animate 
      attributeName="r" 
      values="25;30;25" 
      dur="3s" 
      repeatCount="indefinite" />
    <animate 
      attributeName="opacity" 
      values="0.3;0.1;0.3" 
      dur="3s" 
      repeatCount="indefinite" />
  </circle>
</svg>