document.addEventListener('DOMContentLoaded', function() {
    // Initialize Particles.js with enhanced settings
    particlesJS.load('particles-js', 'js/particles.json', function() {
        console.log('Particles.js loaded successfully');
    });
    
    // Initialize Typed.js for VijinoWorld import/export text animation
    const typedElement = document.querySelector('.typed-text');
    if (typedElement) {
        new Typed(typedElement, {
            strings: [
                'International Trade',
                'Global Logistics',
                'Ocean Freight',
                'Air Cargo',
                'Customs Clearance',
                'Supply Chain Solutions',
                'Import Export Services'
            ],
            typeSpeed: 50,
            backSpeed: 30,
            backDelay: 2000,
            startDelay: 500,
            loop: true,
            showCursor: false
        });
    }
    
    // Mobile Navigation Toggle
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const mobileNav = document.querySelector('.mobile-nav');
    const closeNav = document.querySelector('.close-nav');
    
    if (mobileNavToggle && mobileNav && closeNav) {
        mobileNavToggle.addEventListener('click', function() {
            mobileNav.classList.add('active');
        });
        
        closeNav.addEventListener('click', function() {
            mobileNav.classList.remove('active');
        });
    }
    
    // Countdown Timer with specific launch date
    function updateCountdown() {
        const now = new Date();
        // Set a specific launch date (Year, Month (0-11), Day, Hour, Minute, Second)
        const launchDate = new Date(now.getFullYear(), now.getMonth() + 3, 15, 10, 0, 0);
        
        const diff = launchDate - now;
        
        // If launch date has passed, show zeros
        if (diff <= 0) {
            document.getElementById('days').innerText = '00';
            document.getElementById('hours').innerText = '00';
            document.getElementById('minutes').innerText = '00';
            document.getElementById('seconds').innerText = '00';
            
            // Update launch message with e-commerce theme
            const countdownElement = document.querySelector('.countdown');
            if (countdownElement) {
                const launchMessage = document.createElement('div');
                launchMessage.className = 'launch-message';
                launchMessage.innerHTML = '<span>We are now open!</span><a href="#" class="launch-btn">Shop Now</a>';
                countdownElement.parentNode.replaceChild(launchMessage, countdownElement);
            }
            
            return;
        }
        
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        // Update countdown numbers with animation
        updateCountdownDigit('days', days);
        updateCountdownDigit('hours', hours);
        updateCountdownDigit('minutes', minutes);
        updateCountdownDigit('seconds', seconds);
        
        // Update progress bar if it exists
        updateProgressBar(launchDate, now);
    }
    
    // Helper function to update countdown digits with animation
    function updateCountdownDigit(id, value) {
        const element = document.getElementById(id);
        const currentValue = element.innerText;
        const newValue = value.toString().padStart(2, '0');
        
        if (currentValue !== newValue) {
            // Add flip animation class
            element.classList.add('flip');
            
            // Update the value after a small delay
            setTimeout(() => {
                element.innerText = newValue;
                // Remove the animation class
                setTimeout(() => {
                    element.classList.remove('flip');
                }, 300);
            }, 300);
        } else {
            element.innerText = newValue;
        }
    }
    
    // Update progress bar
    function updateProgressBar(launchDate, now) {
        const progressBar = document.querySelector('.countdown-progress-bar');
        if (progressBar) {
            // Calculate total time period (e.g., 90 days before launch)
            const startDate = new Date(launchDate);
            startDate.setDate(startDate.getDate() - 90); // 90 days before launch
            
            const totalPeriod = launchDate - startDate;
            const elapsed = now - startDate;
            
            // Calculate progress percentage
            let progress = (elapsed / totalPeriod) * 100;
            progress = Math.min(Math.max(progress, 0), 100); // Clamp between 0-100
            
            progressBar.style.width = `${progress}%`;
        }
    }
    
    // Initial call and then update every second
    updateCountdown();
    setInterval(updateCountdown, 1000);
    
    // Newsletter Form Submission
    const newsletterForm = document.getElementById('newsletter-form');
    const formMessage = document.querySelector('.form-message');
    
    if (newsletterForm && formMessage) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            
            // Simulate form submission (in a real app, you would send this to a server)
            formMessage.innerHTML = `<span style="color: #af492b;">Thank you! We'll notify you at ${email} when VijinoWorld launches with exclusive offers.</span>`;
            this.reset();
            
            // Reset message after 5 seconds
            setTimeout(() => {
                formMessage.innerHTML = '';
            }, 5000);
        });
    }
    
    // Smooth Scrolling for Anchor Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Close mobile nav if open
            if (mobileNav && mobileNav.classList.contains('active')) {
                mobileNav.classList.remove('active');
            }
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Update Copyright Year
    document.getElementById('year').innerText = new Date().getFullYear();
    
    // Scroll Reveal Animation
    function revealOnScroll() {
        const reveals = document.querySelectorAll('.reveal');
        
        reveals.forEach(element => {
            const windowHeight = window.innerHeight;
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;
            
            if (elementTop < windowHeight - elementVisible) {
                element.classList.add('active');
            }
        });
    }
    
    window.addEventListener('scroll', revealOnScroll);
    revealOnScroll(); // Initial check
});